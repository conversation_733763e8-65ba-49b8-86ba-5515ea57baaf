/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.seatunnel.connectors.seatunnel.file.sftp.config;

import java.io.Serializable;
import java.util.Properties;

/**
 * SFTP连接配置类
 *
 * <p>用于管理SFTP连接的各种参数，特别是针对"connection is closed by foreign host"问题的优化配置
 */
public class SFTPConnectionConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    // 默认配置值
    public static final long DEFAULT_CONNECTION_TIMEOUT_MS = 60000; // 60秒
    public static final int DEFAULT_MAX_RETRY_ATTEMPTS = 8;
    public static final long DEFAULT_SERVER_ALIVE_INTERVAL = 30000; // 30秒
    public static final int DEFAULT_SERVER_ALIVE_COUNT_MAX = 3;
    public static final int DEFAULT_MAX_AUTH_TRIES = 2;
    public static final long DEFAULT_SOCKET_TIMEOUT_MS = 60000; // 60秒

    // 连接参数
    private long connectionTimeoutMs = DEFAULT_CONNECTION_TIMEOUT_MS;
    private int maxRetryAttempts = DEFAULT_MAX_RETRY_ATTEMPTS;
    private long serverAliveInterval = DEFAULT_SERVER_ALIVE_INTERVAL;
    private int serverAliveCountMax = DEFAULT_SERVER_ALIVE_COUNT_MAX;
    private int maxAuthTries = DEFAULT_MAX_AUTH_TRIES;
    private long socketTimeoutMs = DEFAULT_SOCKET_TIMEOUT_MS;

    // 高级配置
    private boolean enableTcpKeepAlive = true;
    private boolean enableCompression = false;
    private boolean strictHostKeyChecking = false;
    private boolean enableGSSAPIAuth = false;
    private boolean enableDNSLookup = false;

    // 网络优化
    private String ipQoS = "lowdelay";
    private String preferredCiphers = "aes128-ctr,aes192-ctr,aes256-ctr";
    private String preferredKexAlgorithms = "diffie-hellman-group14-sha256";
    private String preferredAuthentications = "password,publickey";

    public SFTPConnectionConfig() {
        // 使用默认配置
    }

    /** 创建针对稳定性优化的配置 */
    public static SFTPConnectionConfig createStabilityOptimized() {
        SFTPConnectionConfig config = new SFTPConnectionConfig();
        config.setConnectionTimeoutMs(30000); // 减少连接超时
        config.setServerAliveInterval(30000); // 30秒心跳
        config.setServerAliveCountMax(3); // 增加心跳容忍度
        config.setMaxAuthTries(2); // 减少认证重试
        config.setEnableCompression(false); // 禁用压缩
        return config;
    }

    /** 创建针对高延迟网络优化的配置 */
    public static SFTPConnectionConfig createHighLatencyOptimized() {
        SFTPConnectionConfig config = new SFTPConnectionConfig();
        config.setConnectionTimeoutMs(120000); // 2分钟超时
        config.setServerAliveInterval(60000); // 1分钟心跳
        config.setServerAliveCountMax(5); // 更高的容忍度
        config.setSocketTimeoutMs(120000); // 2分钟socket超时
        return config;
    }

    /** 转换为JSch配置Properties */
    public Properties toJSchProperties() {
        Properties props = new Properties();

        props.put("StrictHostKeyChecking", strictHostKeyChecking ? "yes" : "no");
        props.put("ConnectTimeout", String.valueOf(Math.min(connectionTimeoutMs, 30000)));
        props.put("ServerAliveInterval", String.valueOf(serverAliveInterval));
        props.put("ServerAliveCountMax", String.valueOf(serverAliveCountMax));
        props.put("TCPKeepAlive", enableTcpKeepAlive ? "yes" : "no");
        props.put("Compression", enableCompression ? "yes" : "no");
        props.put("PreferredAuthentications", preferredAuthentications);
        props.put("MaxAuthTries", String.valueOf(maxAuthTries));
        props.put("GSSAPIAuthentication", enableGSSAPIAuth ? "yes" : "no");
        props.put("UseDNS", enableDNSLookup ? "yes" : "no");
        props.put("IPQoS", ipQoS);
        props.put("SocketTimeout", String.valueOf(socketTimeoutMs));

        if (preferredCiphers != null && !preferredCiphers.isEmpty()) {
            props.put("CheckCiphers", preferredCiphers);
        }

        if (preferredKexAlgorithms != null && !preferredKexAlgorithms.isEmpty()) {
            props.put("CheckKexAlgorithms", preferredKexAlgorithms);
        }

        return props;
    }

    // Getters and Setters
    public long getConnectionTimeoutMs() {
        return connectionTimeoutMs;
    }

    public void setConnectionTimeoutMs(long connectionTimeoutMs) {
        this.connectionTimeoutMs = connectionTimeoutMs;
    }

    public int getMaxRetryAttempts() {
        return maxRetryAttempts;
    }

    public void setMaxRetryAttempts(int maxRetryAttempts) {
        this.maxRetryAttempts = maxRetryAttempts;
    }

    public long getServerAliveInterval() {
        return serverAliveInterval;
    }

    public void setServerAliveInterval(long serverAliveInterval) {
        this.serverAliveInterval = serverAliveInterval;
    }

    public int getServerAliveCountMax() {
        return serverAliveCountMax;
    }

    public void setServerAliveCountMax(int serverAliveCountMax) {
        this.serverAliveCountMax = serverAliveCountMax;
    }

    public int getMaxAuthTries() {
        return maxAuthTries;
    }

    public void setMaxAuthTries(int maxAuthTries) {
        this.maxAuthTries = maxAuthTries;
    }

    public long getSocketTimeoutMs() {
        return socketTimeoutMs;
    }

    public void setSocketTimeoutMs(long socketTimeoutMs) {
        this.socketTimeoutMs = socketTimeoutMs;
    }

    public boolean isEnableTcpKeepAlive() {
        return enableTcpKeepAlive;
    }

    public void setEnableTcpKeepAlive(boolean enableTcpKeepAlive) {
        this.enableTcpKeepAlive = enableTcpKeepAlive;
    }

    public boolean isEnableCompression() {
        return enableCompression;
    }

    public void setEnableCompression(boolean enableCompression) {
        this.enableCompression = enableCompression;
    }

    public boolean isStrictHostKeyChecking() {
        return strictHostKeyChecking;
    }

    public void setStrictHostKeyChecking(boolean strictHostKeyChecking) {
        this.strictHostKeyChecking = strictHostKeyChecking;
    }

    public boolean isEnableGSSAPIAuth() {
        return enableGSSAPIAuth;
    }

    public void setEnableGSSAPIAuth(boolean enableGSSAPIAuth) {
        this.enableGSSAPIAuth = enableGSSAPIAuth;
    }

    public boolean isEnableDNSLookup() {
        return enableDNSLookup;
    }

    public void setEnableDNSLookup(boolean enableDNSLookup) {
        this.enableDNSLookup = enableDNSLookup;
    }

    public String getIpQoS() {
        return ipQoS;
    }

    public void setIpQoS(String ipQoS) {
        this.ipQoS = ipQoS;
    }

    public String getPreferredCiphers() {
        return preferredCiphers;
    }

    public void setPreferredCiphers(String preferredCiphers) {
        this.preferredCiphers = preferredCiphers;
    }

    public String getPreferredKexAlgorithms() {
        return preferredKexAlgorithms;
    }

    public void setPreferredKexAlgorithms(String preferredKexAlgorithms) {
        this.preferredKexAlgorithms = preferredKexAlgorithms;
    }

    public String getPreferredAuthentications() {
        return preferredAuthentications;
    }

    public void setPreferredAuthentications(String preferredAuthentications) {
        this.preferredAuthentications = preferredAuthentications;
    }

    @Override
    public String toString() {
        return "SFTPConnectionConfig{"
                + "connectionTimeoutMs="
                + connectionTimeoutMs
                + ", maxRetryAttempts="
                + maxRetryAttempts
                + ", serverAliveInterval="
                + serverAliveInterval
                + ", serverAliveCountMax="
                + serverAliveCountMax
                + ", maxAuthTries="
                + maxAuthTries
                + ", socketTimeoutMs="
                + socketTimeoutMs
                + ", enableTcpKeepAlive="
                + enableTcpKeepAlive
                + ", enableCompression="
                + enableCompression
                + ", strictHostKeyChecking="
                + strictHostKeyChecking
                + '}';
    }
}
