/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.seatunnel.connectors.seatunnel.file.sftp.system;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.Session;

import java.io.IOException;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 简单的SFTP连接管理器 - 不使用连接池
 *
 * <p>特点： 1. 每次都创建新连接，用完即关闭 2. 没有连接池的复杂性 3. 更适合调试和测试 4. 避免连接复用可能带来的问题
 */
public class SimpleSFTPConnectionManager {

    private static final Logger LOG = LoggerFactory.getLogger(SimpleSFTPConnectionManager.class);

    // 连接信息
    private final String host;
    private final int port;
    private final String user;
    private final String password;
    private final String keyFile;
    private final long connectionTimeoutMs;
    private final int maxRetryAttempts;

    // 统计信息
    private final AtomicLong totalConnectionsCreated = new AtomicLong(0);
    private final AtomicLong totalConnectionsClosed = new AtomicLong(0);
    private final AtomicLong activeConnections = new AtomicLong(0);

    public SimpleSFTPConnectionManager(
            String host, int port, String user, String password, String keyFile) {
        this.host = host;
        this.port = port;
        this.user = user;
        this.password = password;
        this.keyFile = keyFile;
        this.connectionTimeoutMs = 60000; // 60秒
        this.maxRetryAttempts = 8; // 8次重试

        LOG.info("Initialized Simple SFTP Connection Manager for {}:{}", host, port);

        // 启动时进行一次连通性检查（非阻塞）
        try {
            testNetworkConnectivity();
            LOG.info("Initial connectivity check passed for {}:{}", host, port);
        } catch (Exception e) {
            LOG.warn("Initial connectivity check failed for {}:{}: {}", host, port, e.getMessage());
            LOG.warn("This may cause connection issues later. Please verify SFTP server status.");
        }
    }

    /** 创建新的SFTP连接 */
    public ChannelSftp createConnection() throws IOException {
        return createConnectionWithRetry(maxRetryAttempts);
    }

    private ChannelSftp createConnectionWithRetry(int maxAttempts) throws IOException {
        IOException lastException = null;

        for (int attempt = 1; attempt <= maxAttempts; attempt++) {
            try {
                ChannelSftp channel = createNewConnection();
                activeConnections.incrementAndGet();

                LOG.debug(
                        "Created SFTP connection (attempt {}), active connections: {}",
                        attempt,
                        activeConnections.get());

                return channel;

            } catch (Exception e) {
                lastException = new IOException("Failed to create SFTP connection", e);
                LOG.warn("Attempt {} to create connection failed: {}", attempt, e.getMessage(), e);

                // 第一次失败时进行详细诊断
                if (attempt == 1) {
                    diagnoseSFTPServer();
                }

                if (attempt < maxAttempts) {
                    try {
                        // 智能退避策略
                        long baseBackoff = 1000; // 基础1秒
                        long backoffTime =
                                Math.min(baseBackoff * (1L << (attempt - 1)), 20000); // 最大20秒
                        long jitter = (long) (Math.random() * 1000); // 随机抖动
                        long totalWait = backoffTime + jitter;

                        LOG.info(
                                "SFTP connection attempt {} failed, retrying in {}ms",
                                attempt,
                                totalWait);
                        Thread.sleep(totalWait);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new IOException("Connection creation interrupted", ie);
                    }
                }
            }
        }

        throw new IOException(
                "Failed to create SFTP connection after " + maxAttempts + " attempts",
                lastException);
    }

    /** 创建新的SFTP连接 */
    private ChannelSftp createNewConnection() throws IOException {
        // 首先进行网络连通性测试
        testNetworkConnectivity();

        JSch jsch = new JSch();
        Session session = null;
        ChannelSftp channel = null;

        try {
            // 设置用户名
            String actualUser =
                    (user == null || user.isEmpty()) ? System.getProperty("user.name") : user;
            String actualPassword = password == null ? "" : password;

            // 添加密钥文件
            if (keyFile != null && !keyFile.isEmpty()) {
                jsch.addIdentity(keyFile);
            }

            // 创建会话
            if (port <= 0) {
                session = jsch.getSession(actualUser, host);
            } else {
                session = jsch.getSession(actualUser, host, port);
            }

            session.setPassword(actualPassword);

            // 配置连接参数 - 针对"connection is closed by foreign host"问题优化
            java.util.Properties config = new java.util.Properties();
            config.put("StrictHostKeyChecking", "no");
            config.put("ConnectTimeout", String.valueOf(connectionTimeoutMs));

            // 增强的连接稳定性配置
            config.put("ServerAliveInterval", "30000"); // 30秒心跳，避免过于频繁
            config.put("ServerAliveCountMax", "3"); // 增加心跳失败容忍次数
            config.put("TCPKeepAlive", "yes"); // 启用TCP keepalive
            config.put("Compression", "no"); // 禁用压缩，减少CPU开销和连接复杂性

            // 认证相关优化
            config.put("PreferredAuthentications", "password,publickey");
            config.put("MaxAuthTries", "2"); // 减少认证重试次数，避免被服务器拒绝
            config.put("GSSAPIAuthentication", "no"); // 禁用GSSAPI认证
            config.put("UseDNS", "no"); // 禁用DNS查找，加快连接速度

            // 网络层优化
            config.put("IPQoS", "lowdelay"); // 优化延迟
            config.put("CheckCiphers", "aes128-ctr,aes192-ctr,aes256-ctr"); // 使用更快的加密算法
            config.put("CheckKexAlgorithms", "diffie-hellman-group14-sha256"); // 使用稳定的密钥交换算法

            // 连接保持和超时设置
            config.put("ConnectTimeout", String.valueOf(Math.min(connectionTimeoutMs, 30000))); // 限制最大连接超时
            config.put("SocketTimeout", "60000"); // 设置socket超时

            session.setConfig(config);

            LOG.debug("Connecting to SFTP server {}:{} with user: {} (enhanced stability mode)", host, port, actualUser);

            // 分阶段建立连接，便于诊断问题
            LOG.debug("Step 1: Establishing SSH session...");
            session.connect((int) Math.min(connectionTimeoutMs, 30000));

            LOG.debug("Step 2: Opening SFTP channel...");
            channel = (ChannelSftp) session.openChannel("sftp");

            LOG.debug("Step 3: Connecting SFTP channel...");
            channel.connect((int) Math.min(connectionTimeoutMs, 30000));

            // 验证连接是否真正可用
            LOG.debug("Step 4: Validating SFTP connection...");
            String pwd = channel.pwd();
            LOG.debug("SFTP connection validated, current directory: {}", pwd);

            totalConnectionsCreated.incrementAndGet();

            LOG.debug(
                    "Successfully created SFTP connection to {}:{}, total created: {}",
                    host,
                    port,
                    totalConnectionsCreated.get());

            return channel;

        } catch (Exception e) {
            // 详细的错误分析和清理
            String errorMsg = analyzeConnectionError(e);

            // 清理资源
            if (channel != null) {
                try {
                    channel.disconnect();
                } catch (Exception ex) {
                    LOG.debug("Error disconnecting channel during cleanup: {}", ex.getMessage());
                }
            }
            if (session != null) {
                try {
                    session.disconnect();
                } catch (Exception ex) {
                    LOG.debug("Error disconnecting session during cleanup: {}", ex.getMessage());
                }
            }

            throw new IOException(
                    "Failed to create SFTP connection to " + host + ":" + port + ". " + errorMsg,
                    e);
        }
    }

    /** 分析连接错误并提供解决建议 */
    private String analyzeConnectionError(Exception e) {
        String errorMsg = e.getMessage();
        if (errorMsg == null) {
            errorMsg = e.getClass().getSimpleName();
        }

        StringBuilder analysis = new StringBuilder();
        analysis.append("Error: ").append(errorMsg);

        if (errorMsg.contains("connection is closed by foreign host")) {
            analysis.append("\n解决建议：")
                   .append("\n1. 检查SFTP服务器是否正常运行")
                   .append("\n2. 验证服务器连接数限制配置")
                   .append("\n3. 检查防火墙和网络策略")
                   .append("\n4. 确认用户认证信息正确")
                   .append("\n5. 检查服务器负载情况");
        } else if (errorMsg.contains("Auth fail") || errorMsg.contains("Authentication")) {
            analysis.append("\n解决建议：")
                   .append("\n1. 验证用户名和密码")
                   .append("\n2. 检查SSH密钥配置")
                   .append("\n3. 确认用户有SFTP访问权限");
        } else if (errorMsg.contains("timeout") || errorMsg.contains("Connection timed out")) {
            analysis.append("\n解决建议：")
                   .append("\n1. 检查网络连通性")
                   .append("\n2. 增加连接超时时间")
                   .append("\n3. 检查服务器响应速度");
        }

        return analysis.toString();
    }

    /** 关闭SFTP连接 */
    public void closeConnection(ChannelSftp channel) {
        if (channel == null) {
            return;
        }

        try {
            activeConnections.decrementAndGet();
            totalConnectionsClosed.incrementAndGet();

            if (channel.isConnected()) {
                channel.disconnect();
            }

            if (channel.getSession() != null && channel.getSession().isConnected()) {
                channel.getSession().disconnect();
            }

            LOG.debug(
                    "Closed SFTP connection, active: {}, total closed: {}",
                    activeConnections.get(),
                    totalConnectionsClosed.get());

        } catch (Exception e) {
            LOG.warn("Error closing SFTP connection: {}", e.getMessage());
        }
    }

    /** 验证连接是否有效 */
    public boolean isConnectionValid(ChannelSftp channel) {
        try {
            if (channel == null || !channel.isConnected()) {
                return false;
            }

            if (channel.getSession() == null || !channel.getSession().isConnected()) {
                return false;
            }

            // 执行简单命令验证连接
            channel.pwd();
            return true;
        } catch (Exception e) {
            LOG.debug("Connection validation failed: {}", e.getMessage());
            return false;
        }
    }

    /** 获取连接统计信息 */
    public String getConnectionStats() {
        return String.format(
                "SFTP Connection Stats - Active: %d, Created: %d, Closed: %d",
                activeConnections.get(),
                totalConnectionsCreated.get(),
                totalConnectionsClosed.get());
    }

    public long getActiveConnectionCount() {
        return activeConnections.get();
    }

    public long getTotalConnectionsCreated() {
        return totalConnectionsCreated.get();
    }

    public long getTotalConnectionsClosed() {
        return totalConnectionsClosed.get();
    }

    /** 公共诊断方法 */
    public void performDiagnostics() {
        diagnoseSFTPServer();
    }

    /** 测试网络连通性 */
    private void testNetworkConnectivity() throws IOException {
        LOG.debug("Testing network connectivity to {}:{}", host, port);

        // 多次尝试连接测试，提高可靠性
        int maxAttempts = 3;
        IOException lastException = null;

        for (int attempt = 1; attempt <= maxAttempts; attempt++) {
            try (java.net.Socket socket = new java.net.Socket()) {
                long startTime = System.currentTimeMillis();

                // 设置socket选项
                socket.setTcpNoDelay(true);
                socket.setKeepAlive(true);
                socket.setSoTimeout(10000); // 10秒读取超时

                socket.connect(new java.net.InetSocketAddress(host, port), 10000);

                long connectTime = System.currentTimeMillis() - startTime;
                LOG.debug("Network connectivity test passed for {}:{} in {}ms (attempt {})",
                         host, port, connectTime, attempt);

                // 连接成功，额外验证端口是否真正可用
                if (isPortResponding(socket)) {
                    LOG.debug("Port {} on {} is responding correctly", port, host);
                    return; // 测试成功
                } else {
                    LOG.warn("Port {} on {} is not responding as expected (attempt {})", port, host, attempt);
                }

            } catch (Exception e) {
                lastException = new IOException("Network connectivity test failed", e);
                LOG.warn("Network connectivity test attempt {} failed for {}:{}: {}",
                        attempt, host, port, e.getMessage());

                if (attempt < maxAttempts) {
                    try {
                        Thread.sleep(1000 * attempt); // 递增等待时间
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new IOException("Connectivity test interrupted", ie);
                    }
                }
            }
        }

        // 所有尝试都失败
        LOG.error("Network connectivity test FAILED for {}:{} after {} attempts", host, port, maxAttempts);
        throw new IOException(
                "Cannot reach SFTP server " + host + ":" + port +
                " after " + maxAttempts + " attempts. " +
                "Please check: 1) Network connectivity 2) Firewall settings 3) SFTP service status 4) Server load",
                lastException);
    }

    /** 验证端口是否正确响应 */
    private boolean isPortResponding(java.net.Socket socket) {
        try {
            // 对于SSH/SFTP端口，服务器通常会发送版本信息
            socket.setSoTimeout(5000); // 5秒超时
            java.io.InputStream is = socket.getInputStream();

            // 读取一些数据来验证服务是否响应
            byte[] buffer = new byte[256];
            int bytesRead = is.read(buffer, 0, buffer.length);

            if (bytesRead > 0) {
                String response = new String(buffer, 0, bytesRead);
                LOG.debug("Received response from {}:{}: {}", host, port,
                         response.substring(0, Math.min(50, response.length())));

                // SSH服务器通常以"SSH-"开头
                return response.startsWith("SSH-");
            }

            return false;
        } catch (Exception e) {
            LOG.debug("Port response check failed: {}", e.getMessage());
            return true; // 如果无法验证，假设端口正常（避免误报）
        }
    }

    /** 诊断SFTP服务器状态 */
    private void diagnoseSFTPServer() {
        try {
            LOG.info("=== SFTP Connection Diagnostics for {}:{} ===", host, port);

            // 1. 基础网络连通性测试
            LOG.info("1. Testing basic TCP connectivity...");
            long startTime = System.currentTimeMillis();
            try (java.net.Socket socket = new java.net.Socket()) {
                socket.connect(new java.net.InetSocketAddress(host, port), 5000);
                long connectTime = System.currentTimeMillis() - startTime;
                LOG.info("   ✓ TCP connection successful in {}ms", connectTime);

                // 测试SSH服务响应
                try {
                    socket.setSoTimeout(3000);
                    java.io.InputStream is = socket.getInputStream();
                    byte[] buffer = new byte[256];
                    int bytesRead = is.read(buffer, 0, buffer.length);
                    if (bytesRead > 0) {
                        String response = new String(buffer, 0, Math.min(bytesRead, 50));
                        LOG.info("   ✓ SSH service response: {}", response.trim());
                    }
                } catch (Exception e) {
                    LOG.warn("   ⚠ Could not read SSH banner: {}", e.getMessage());
                }

            } catch (Exception e) {
                LOG.error("   ✗ TCP connection failed: {}", e.getMessage());

                // 提供详细的网络诊断建议
                if (e instanceof java.net.ConnectException) {
                    LOG.error("   建议：检查目标服务器是否运行，端口是否正确");
                } else if (e instanceof java.net.SocketTimeoutException) {
                    LOG.error("   建议：检查网络延迟，考虑增加超时时间");
                } else if (e instanceof java.net.UnknownHostException) {
                    LOG.error("   建议：检查主机名解析，确认DNS配置");
                }
                return;
            }

            // 2. 系统信息
            LOG.info("2. System information:");
            LOG.info("   - Java version: {}", System.getProperty("java.version"));
            LOG.info("   - OS: {} {}", System.getProperty("os.name"), System.getProperty("os.version"));
            LOG.info("   - Available processors: {}", Runtime.getRuntime().availableProcessors());
            LOG.info("   - Max memory: {}MB", Runtime.getRuntime().maxMemory() / 1024 / 1024);

            // 3. 连接统计
            LOG.info("3. Current connection stats: {}", getConnectionStats());

            // 4. 连接参数
            LOG.info("4. Connection parameters:");
            LOG.info("   - Host: {}", host);
            LOG.info("   - Port: {}", port);
            LOG.info("   - User: {}", user != null ? user : "default");
            LOG.info("   - Has password: {}", password != null && !password.isEmpty());
            LOG.info("   - Key file: {}", keyFile != null ? keyFile : "none");
            LOG.info("   - Timeout: {}ms", connectionTimeoutMs);
            LOG.info("   - Max retries: {}", maxRetryAttempts);

            // 5. 网络环境检查
            LOG.info("5. Network environment:");
            try {
                java.net.InetAddress addr = java.net.InetAddress.getByName(host);
                LOG.info("   - Resolved IP: {}", addr.getHostAddress());
                LOG.info("   - Is reachable: {}", addr.isReachable(5000));
            } catch (Exception e) {
                LOG.warn("   - DNS resolution failed: {}", e.getMessage());
            }

            // 6. 建议
            LOG.info("6. Troubleshooting suggestions:");
            LOG.info("   - Verify SFTP service is running on target server");
            LOG.info("   - Check firewall rules and network policies");
            LOG.info("   - Confirm user credentials and permissions");
            LOG.info("   - Monitor server load and connection limits");
            LOG.info("   - Consider using SSH key authentication");

        } catch (Exception e) {
            LOG.warn("Diagnostics failed: {}", e.getMessage());
        }
    }
}
