/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.seatunnel.connectors.seatunnel.file.sftp.system;

import org.apache.seatunnel.connectors.seatunnel.file.sftp.config.SFTPConnectionConfig;
import org.apache.seatunnel.connectors.seatunnel.file.sftp.utils.SFTPConnectionTroubleshooter;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.Session;

import java.io.IOException;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 简单的SFTP连接管理器 - 不使用连接池
 *
 * <p>特点： 1. 每次都创建新连接，用完即关闭 2. 没有连接池的复杂性 3. 更适合调试和测试 4. 避免连接复用可能带来的问题
 */
public class SimpleSFTPConnectionManager {

    private static final Logger LOG = LoggerFactory.getLogger(SimpleSFTPConnectionManager.class);

    // 连接信息
    private final String host;
    private final int port;
    private final String user;
    private final String password;
    private final String keyFile;
    private final SFTPConnectionConfig config;

    // 统计信息
    private final AtomicLong totalConnectionsCreated = new AtomicLong(0);
    private final AtomicLong totalConnectionsClosed = new AtomicLong(0);
    private final AtomicLong activeConnections = new AtomicLong(0);
    private final AtomicLong totalConnectionFailures = new AtomicLong(0);

    public SimpleSFTPConnectionManager(
            String host, int port, String user, String password, String keyFile) {
        this(host, port, user, password, keyFile, SFTPConnectionConfig.createStabilityOptimized());
    }

    public SimpleSFTPConnectionManager(
            String host,
            int port,
            String user,
            String password,
            String keyFile,
            SFTPConnectionConfig config) {
        this.host = host;
        this.port = port;
        this.user = user;
        this.password = password;
        this.keyFile = keyFile;
        this.config = config != null ? config : SFTPConnectionConfig.createStabilityOptimized();

        LOG.info(
                "Initialized Simple SFTP Connection Manager for {}:{} with config: {}",
                host,
                port,
                this.config);

        // 启动时进行一次连通性检查（非阻塞）
        performInitialDiagnostics();
    }

    /** 执行初始诊断 */
    private void performInitialDiagnostics() {
        try {
            LOG.info("Performing initial SFTP connection diagnostics...");
            SFTPConnectionTroubleshooter.DiagnosticResult result =
                    SFTPConnectionTroubleshooter.diagnoseConnection(host, port);

            if (result.isSuccess()) {
                LOG.info(
                        "Initial connectivity check passed for {}:{} ({}ms)",
                        host,
                        port,
                        result.getResponseTimeMs());
            } else {
                LOG.warn(
                        "Initial connectivity check failed for {}:{}: {}",
                        host,
                        port,
                        result.getMessage());

                if (!result.getSuggestions().isEmpty()) {
                    LOG.warn("Troubleshooting suggestions:");
                    for (String suggestion : result.getSuggestions()) {
                        LOG.warn("  - {}", suggestion);
                    }
                }
                LOG.warn(
                        "This may cause connection issues later. Please verify SFTP server status.");
            }
        } catch (Exception e) {
            LOG.warn("Initial diagnostics failed: {}", e.getMessage());
        }
    }

    /** 创建新的SFTP连接 */
    public ChannelSftp createConnection() throws IOException {
        return createConnectionWithRetry(config.getMaxRetryAttempts());
    }

    private ChannelSftp createConnectionWithRetry(int maxAttempts) throws IOException {
        IOException lastException = null;

        for (int attempt = 1; attempt <= maxAttempts; attempt++) {
            try {
                ChannelSftp channel = createNewConnection();
                activeConnections.incrementAndGet();

                LOG.debug(
                        "Created SFTP connection (attempt {}), active connections: {}",
                        attempt,
                        activeConnections.get());

                return channel;

            } catch (Exception e) {
                totalConnectionFailures.incrementAndGet();
                lastException = new IOException("Failed to create SFTP connection", e);

                // 详细的错误分析
                String errorAnalysis = SFTPConnectionTroubleshooter.analyzeConnectionError(e);
                LOG.warn("Attempt {} to create connection failed:\n{}", attempt, errorAnalysis);

                // 第一次失败时进行详细诊断
                if (attempt == 1) {
                    diagnoseSFTPServer();
                }

                if (attempt < maxAttempts) {
                    try {
                        // 智能退避策略 - 根据错误类型调整
                        long baseBackoff = getBackoffTime(e, attempt);
                        long jitter = (long) (Math.random() * 1000); // 随机抖动
                        long totalWait = baseBackoff + jitter;

                        LOG.info(
                                "SFTP connection attempt {} failed, retrying in {}ms (error: {})",
                                attempt,
                                totalWait,
                                e.getMessage());
                        Thread.sleep(totalWait);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new IOException("Connection creation interrupted", ie);
                    }
                }
            }
        }

        // 记录最终失败统计
        LOG.error(
                "Failed to create SFTP connection after {} attempts. Total failures: {}",
                maxAttempts,
                totalConnectionFailures.get());

        throw new IOException(
                "Failed to create SFTP connection after "
                        + maxAttempts
                        + " attempts. "
                        + "Please check server status and network connectivity.",
                lastException);
    }

    /** 根据错误类型计算退避时间 */
    private long getBackoffTime(Exception e, int attempt) {
        String errorMsg = e.getMessage();
        long baseBackoff = 1000; // 基础1秒

        if (errorMsg != null && errorMsg.contains("connection is closed by foreign host")) {
            // 对于连接被拒绝的情况，使用更长的退避时间
            baseBackoff = 3000; // 基础3秒
        } else if (errorMsg != null && errorMsg.contains("timeout")) {
            // 对于超时，使用中等退避时间
            baseBackoff = 2000; // 基础2秒
        }

        // 指数退避，但限制最大值
        return Math.min(baseBackoff * (1L << (attempt - 1)), 30000); // 最大30秒
    }

    /** 创建新的SFTP连接 */
    private ChannelSftp createNewConnection() throws IOException {
        // 首先进行网络连通性测试
        testNetworkConnectivity();

        JSch jsch = new JSch();
        Session session = null;
        ChannelSftp channel = null;

        try {
            // 设置用户名
            String actualUser =
                    (user == null || user.isEmpty()) ? System.getProperty("user.name") : user;
            String actualPassword = password == null ? "" : password;

            // 添加密钥文件
            if (keyFile != null && !keyFile.isEmpty()) {
                jsch.addIdentity(keyFile);
            }

            // 创建会话
            if (port <= 0) {
                session = jsch.getSession(actualUser, host);
            } else {
                session = jsch.getSession(actualUser, host, port);
            }

            session.setPassword(actualPassword);

            // 使用配置类生成JSch配置
            java.util.Properties jschConfig = config.toJSchProperties();
            session.setConfig(jschConfig);

            LOG.debug(
                    "Connecting to SFTP server {}:{} with user: {} (enhanced stability mode)",
                    host,
                    port,
                    actualUser);

            // 分阶段建立连接，便于诊断问题
            long connectTimeout = Math.min(config.getConnectionTimeoutMs(), 30000);

            LOG.debug("Step 1: Establishing SSH session...");
            session.connect((int) connectTimeout);
            session.setServerAliveInterval(60000);
            session.setTimeout(120000);

            LOG.debug("Step 2: Opening SFTP channel...");
            channel = (ChannelSftp) session.openChannel("sftp");

            LOG.debug("Step 3: Connecting SFTP channel...");
            channel.connect((int) connectTimeout);

            // 验证连接是否真正可用
            LOG.debug("Step 4: Validating SFTP connection...");
            String pwd = channel.pwd();
            LOG.debug("SFTP connection validated, current directory: {}", pwd);

            totalConnectionsCreated.incrementAndGet();

            LOG.debug(
                    "Successfully created SFTP connection to {}:{}, total created: {}",
                    host,
                    port,
                    totalConnectionsCreated.get());

            return channel;

        } catch (Exception e) {
            // 详细的错误分析和清理
            String errorMsg = analyzeConnectionError(e);

            // 清理资源
            if (channel != null) {
                try {
                    channel.disconnect();
                } catch (Exception ex) {
                    LOG.debug("Error disconnecting channel during cleanup: {}", ex.getMessage());
                }
            }
            if (session != null) {
                try {
                    session.disconnect();
                } catch (Exception ex) {
                    LOG.debug("Error disconnecting session during cleanup: {}", ex.getMessage());
                }
            }

            throw new IOException(
                    "Failed to create SFTP connection to " + host + ":" + port + ". " + errorMsg,
                    e);
        }
    }

    /** 分析连接错误并提供解决建议 */
    private String analyzeConnectionError(Exception e) {
        String errorMsg = e.getMessage();
        if (errorMsg == null) {
            errorMsg = e.getClass().getSimpleName();
        }

        StringBuilder analysis = new StringBuilder();
        analysis.append("Error: ").append(errorMsg);

        if (errorMsg.contains("connection is closed by foreign host")) {
            analysis.append("\n解决建议：")
                    .append("\n1. 检查SFTP服务器是否正常运行")
                    .append("\n2. 验证服务器连接数限制配置")
                    .append("\n3. 检查防火墙和网络策略")
                    .append("\n4. 确认用户认证信息正确")
                    .append("\n5. 检查服务器负载情况");
        } else if (errorMsg.contains("Auth fail") || errorMsg.contains("Authentication")) {
            analysis.append("\n解决建议：")
                    .append("\n1. 验证用户名和密码")
                    .append("\n2. 检查SSH密钥配置")
                    .append("\n3. 确认用户有SFTP访问权限");
        } else if (errorMsg.contains("timeout") || errorMsg.contains("Connection timed out")) {
            analysis.append("\n解决建议：")
                    .append("\n1. 检查网络连通性")
                    .append("\n2. 增加连接超时时间")
                    .append("\n3. 检查服务器响应速度");
        }

        return analysis.toString();
    }

    /** 关闭SFTP连接 */
    public void closeConnection(ChannelSftp channel) {
        if (channel == null) {
            return;
        }

        try {
            activeConnections.decrementAndGet();
            totalConnectionsClosed.incrementAndGet();

            if (channel.isConnected()) {
                channel.disconnect();
            }

            if (channel.getSession() != null && channel.getSession().isConnected()) {
                channel.getSession().disconnect();
            }

            LOG.debug(
                    "Closed SFTP connection, active: {}, total closed: {}",
                    activeConnections.get(),
                    totalConnectionsClosed.get());

        } catch (Exception e) {
            LOG.warn("Error closing SFTP connection: {}", e.getMessage());
        }
    }

    /** 验证连接是否有效 */
    public boolean isConnectionValid(ChannelSftp channel) {
        try {
            if (channel == null || !channel.isConnected()) {
                return false;
            }

            if (channel.getSession() == null || !channel.getSession().isConnected()) {
                return false;
            }

            // 执行简单命令验证连接
            channel.pwd();
            return true;
        } catch (Exception e) {
            LOG.debug("Connection validation failed: {}", e.getMessage());
            return false;
        }
    }

    /** 获取连接统计信息 */
    public String getConnectionStats() {
        return String.format(
                "SFTP Connection Stats - Active: %d, Created: %d, Closed: %d, Failures: %d",
                activeConnections.get(),
                totalConnectionsCreated.get(),
                totalConnectionsClosed.get(),
                totalConnectionFailures.get());
    }

    public long getActiveConnectionCount() {
        return activeConnections.get();
    }

    public long getTotalConnectionsCreated() {
        return totalConnectionsCreated.get();
    }

    public long getTotalConnectionsClosed() {
        return totalConnectionsClosed.get();
    }

    public long getTotalConnectionFailures() {
        return totalConnectionFailures.get();
    }

    public SFTPConnectionConfig getConfig() {
        return config;
    }

    /** 公共诊断方法 */
    public void performDiagnostics() {
        diagnoseSFTPServer();
    }

    /** 测试网络连通性 */
    private void testNetworkConnectivity() throws IOException {
        LOG.debug("Testing network connectivity to {}:{}", host, port);

        // 多次尝试连接测试，提高可靠性
        int maxAttempts = 3;
        IOException lastException = null;

        for (int attempt = 1; attempt <= maxAttempts; attempt++) {
            try (java.net.Socket socket = new java.net.Socket()) {
                long startTime = System.currentTimeMillis();

                // 设置socket选项
                socket.setTcpNoDelay(true);
                socket.setKeepAlive(true);
                socket.setSoTimeout(10000); // 10秒读取超时

                socket.connect(new java.net.InetSocketAddress(host, port), 10000);

                long connectTime = System.currentTimeMillis() - startTime;
                LOG.debug(
                        "Network connectivity test passed for {}:{} in {}ms (attempt {})",
                        host,
                        port,
                        connectTime,
                        attempt);

                // 连接成功，额外验证端口是否真正可用
                if (isPortResponding(socket)) {
                    LOG.debug("Port {} on {} is responding correctly", port, host);
                    return; // 测试成功
                } else {
                    LOG.warn(
                            "Port {} on {} is not responding as expected (attempt {})",
                            port,
                            host,
                            attempt);
                }

            } catch (Exception e) {
                lastException = new IOException("Network connectivity test failed", e);
                LOG.warn(
                        "Network connectivity test attempt {} failed for {}:{}: {}",
                        attempt,
                        host,
                        port,
                        e.getMessage());

                if (attempt < maxAttempts) {
                    try {
                        Thread.sleep(1000 * attempt); // 递增等待时间
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new IOException("Connectivity test interrupted", ie);
                    }
                }
            }
        }

        // 所有尝试都失败
        LOG.error(
                "Network connectivity test FAILED for {}:{} after {} attempts",
                host,
                port,
                maxAttempts);
        throw new IOException(
                "Cannot reach SFTP server "
                        + host
                        + ":"
                        + port
                        + " after "
                        + maxAttempts
                        + " attempts. "
                        + "Please check: 1) Network connectivity 2) Firewall settings 3) SFTP service status 4) Server load",
                lastException);
    }

    /** 验证端口是否正确响应 */
    private boolean isPortResponding(java.net.Socket socket) {
        try {
            // 对于SSH/SFTP端口，服务器通常会发送版本信息
            socket.setSoTimeout(5000); // 5秒超时
            java.io.InputStream is = socket.getInputStream();

            // 读取一些数据来验证服务是否响应
            byte[] buffer = new byte[256];
            int bytesRead = is.read(buffer, 0, buffer.length);

            if (bytesRead > 0) {
                String response = new String(buffer, 0, bytesRead);
                LOG.debug(
                        "Received response from {}:{}: {}",
                        host,
                        port,
                        response.substring(0, Math.min(50, response.length())));

                // SSH服务器通常以"SSH-"开头
                return response.startsWith("SSH-");
            }

            return false;
        } catch (Exception e) {
            LOG.debug("Port response check failed: {}", e.getMessage());
            return true; // 如果无法验证，假设端口正常（避免误报）
        }
    }

    /** 诊断SFTP服务器状态 */
    private void diagnoseSFTPServer() {
        try {
            LOG.info("=== SFTP Connection Diagnostics for {}:{} ===", host, port);

            // 1. 基础网络连通性测试
            LOG.info("1. Testing basic TCP connectivity...");
            long startTime = System.currentTimeMillis();
            try (java.net.Socket socket = new java.net.Socket()) {
                socket.connect(new java.net.InetSocketAddress(host, port), 5000);
                long connectTime = System.currentTimeMillis() - startTime;
                LOG.info("   ✓ TCP connection successful in {}ms", connectTime);

                // 测试SSH服务响应
                try {
                    socket.setSoTimeout(3000);
                    java.io.InputStream is = socket.getInputStream();
                    byte[] buffer = new byte[256];
                    int bytesRead = is.read(buffer, 0, buffer.length);
                    if (bytesRead > 0) {
                        String response = new String(buffer, 0, Math.min(bytesRead, 50));
                        LOG.info("   ✓ SSH service response: {}", response.trim());
                    }
                } catch (Exception e) {
                    LOG.warn("   ⚠ Could not read SSH banner: {}", e.getMessage());
                }

            } catch (Exception e) {
                LOG.error("   ✗ TCP connection failed: {}", e.getMessage());

                // 提供详细的网络诊断建议
                if (e instanceof java.net.ConnectException) {
                    LOG.error("   建议：检查目标服务器是否运行，端口是否正确");
                } else if (e instanceof java.net.SocketTimeoutException) {
                    LOG.error("   建议：检查网络延迟，考虑增加超时时间");
                } else if (e instanceof java.net.UnknownHostException) {
                    LOG.error("   建议：检查主机名解析，确认DNS配置");
                }
                return;
            }

            // 2. 系统信息
            LOG.info("2. System information:");
            LOG.info("   - Java version: {}", System.getProperty("java.version"));
            LOG.info(
                    "   - OS: {} {}",
                    System.getProperty("os.name"),
                    System.getProperty("os.version"));
            LOG.info("   - Available processors: {}", Runtime.getRuntime().availableProcessors());
            LOG.info("   - Max memory: {}MB", Runtime.getRuntime().maxMemory() / 1024 / 1024);

            // 3. 连接统计
            LOG.info("3. Current connection stats: {}", getConnectionStats());

            // 4. 连接参数
            LOG.info("4. Connection parameters:");
            LOG.info("   - Host: {}", host);
            LOG.info("   - Port: {}", port);
            LOG.info("   - User: {}", user != null ? user : "default");
            LOG.info("   - Has password: {}", password != null && !password.isEmpty());
            LOG.info("   - Key file: {}", keyFile != null ? keyFile : "none");
            // 5. 网络环境检查
            LOG.info("5. Network environment:");
            try {
                java.net.InetAddress addr = java.net.InetAddress.getByName(host);
                LOG.info("   - Resolved IP: {}", addr.getHostAddress());
                LOG.info("   - Is reachable: {}", addr.isReachable(5000));
            } catch (Exception e) {
                LOG.warn("   - DNS resolution failed: {}", e.getMessage());
            }

            // 6. 建议
            LOG.info("6. Troubleshooting suggestions:");
            LOG.info("   - Verify SFTP service is running on target server");
            LOG.info("   - Check firewall rules and network policies");
            LOG.info("   - Confirm user credentials and permissions");
            LOG.info("   - Monitor server load and connection limits");
            LOG.info("   - Consider using SSH key authentication");

        } catch (Exception e) {
            LOG.warn("Diagnostics failed: {}", e.getMessage());
        }
    }
}
