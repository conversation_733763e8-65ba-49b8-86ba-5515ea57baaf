/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.seatunnel.connectors.seatunnel.file.sftp.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.InetAddress;
import java.net.InetSocketAddress;
import java.net.Socket;
import java.util.ArrayList;
import java.util.List;

/**
 * SFTP连接问题诊断和故障排除工具
 *
 * <p>专门用于诊断"connection is closed by foreign host"等SFTP连接问题
 */
public class SFTPConnectionTroubleshooter {

    private static final Logger LOG = LoggerFactory.getLogger(SFTPConnectionTroubleshooter.class);

    /** 诊断结果 */
    public static class DiagnosticResult {
        private final boolean success;
        private final String message;
        private final List<String> suggestions;
        private final long responseTimeMs;

        public DiagnosticResult(
                boolean success, String message, List<String> suggestions, long responseTimeMs) {
            this.success = success;
            this.message = message;
            this.suggestions = suggestions != null ? suggestions : new ArrayList<>();
            this.responseTimeMs = responseTimeMs;
        }

        public boolean isSuccess() {
            return success;
        }

        public String getMessage() {
            return message;
        }

        public List<String> getSuggestions() {
            return suggestions;
        }

        public long getResponseTimeMs() {
            return responseTimeMs;
        }

        @Override
        public String toString() {
            StringBuilder sb = new StringBuilder();
            sb.append("DiagnosticResult{")
                    .append("success=")
                    .append(success)
                    .append(", message='")
                    .append(message)
                    .append('\'')
                    .append(", responseTime=")
                    .append(responseTimeMs)
                    .append("ms");

            if (!suggestions.isEmpty()) {
                sb.append(", suggestions=[");
                for (int i = 0; i < suggestions.size(); i++) {
                    if (i > 0) sb.append(", ");
                    sb.append(suggestions.get(i));
                }
                sb.append("]");
            }
            sb.append('}');
            return sb.toString();
        }
    }

    /** 执行完整的SFTP连接诊断 */
    public static DiagnosticResult diagnoseConnection(String host, int port) {
        LOG.info("开始诊断SFTP连接: {}:{}", host, port);

        List<String> allSuggestions = new ArrayList<>();
        long totalStartTime = System.currentTimeMillis();

        try {
            // 1. DNS解析测试
            DiagnosticResult dnsResult = testDNSResolution(host);
            if (!dnsResult.isSuccess()) {
                allSuggestions.addAll(dnsResult.getSuggestions());
                return new DiagnosticResult(
                        false,
                        "DNS解析失败: " + dnsResult.getMessage(),
                        allSuggestions,
                        System.currentTimeMillis() - totalStartTime);
            }

            // 2. 网络连通性测试
            DiagnosticResult connectivityResult = testNetworkConnectivity(host, port);
            if (!connectivityResult.isSuccess()) {
                allSuggestions.addAll(connectivityResult.getSuggestions());
                return new DiagnosticResult(
                        false,
                        "网络连通性测试失败: " + connectivityResult.getMessage(),
                        allSuggestions,
                        System.currentTimeMillis() - totalStartTime);
            }

            // 3. SSH服务响应测试
            DiagnosticResult sshResult = testSSHServiceResponse(host, port);
            if (!sshResult.isSuccess()) {
                allSuggestions.addAll(sshResult.getSuggestions());
                return new DiagnosticResult(
                        false,
                        "SSH服务响应测试失败: " + sshResult.getMessage(),
                        allSuggestions,
                        System.currentTimeMillis() - totalStartTime);
            }

            // 4. 连接稳定性测试
            DiagnosticResult stabilityResult = testConnectionStability(host, port);
            allSuggestions.addAll(stabilityResult.getSuggestions());

            long totalTime = System.currentTimeMillis() - totalStartTime;
            String message = String.format("所有诊断测试通过，连接看起来正常 (总耗时: %dms)", totalTime);

            return new DiagnosticResult(true, message, allSuggestions, totalTime);

        } catch (Exception e) {
            LOG.error("诊断过程中发生异常", e);
            allSuggestions.add("诊断过程异常，请检查网络环境和系统配置");
            return new DiagnosticResult(
                    false,
                    "诊断异常: " + e.getMessage(),
                    allSuggestions,
                    System.currentTimeMillis() - totalStartTime);
        }
    }

    /** 测试DNS解析 */
    public static DiagnosticResult testDNSResolution(String host) {
        long startTime = System.currentTimeMillis();
        List<String> suggestions = new ArrayList<>();

        try {
            InetAddress address = InetAddress.getByName(host);
            long responseTime = System.currentTimeMillis() - startTime;

            String message = String.format("DNS解析成功: %s -> %s", host, address.getHostAddress());
            LOG.debug(message + " (耗时: {}ms)", responseTime);

            if (responseTime > 5000) {
                suggestions.add("DNS解析较慢，考虑使用IP地址直接连接");
            }

            return new DiagnosticResult(true, message, suggestions, responseTime);

        } catch (Exception e) {
            long responseTime = System.currentTimeMillis() - startTime;
            suggestions.add("检查DNS服务器配置");
            suggestions.add("尝试使用IP地址代替主机名");
            suggestions.add("检查网络连接");

            return new DiagnosticResult(false, e.getMessage(), suggestions, responseTime);
        }
    }

    /** 测试网络连通性 */
    public static DiagnosticResult testNetworkConnectivity(String host, int port) {
        long startTime = System.currentTimeMillis();
        List<String> suggestions = new ArrayList<>();

        try (Socket socket = new Socket()) {
            socket.setTcpNoDelay(true);
            socket.setKeepAlive(true);
            socket.setSoTimeout(10000);

            socket.connect(new InetSocketAddress(host, port), 10000);
            long responseTime = System.currentTimeMillis() - startTime;

            String message = String.format("TCP连接成功到 %s:%d", host, port);
            LOG.debug(message + " (耗时: {}ms)", responseTime);

            if (responseTime > 3000) {
                suggestions.add("网络延迟较高，考虑增加连接超时时间");
                suggestions.add("检查网络路由和带宽");
            }

            return new DiagnosticResult(true, message, suggestions, responseTime);

        } catch (Exception e) {
            long responseTime = System.currentTimeMillis() - startTime;

            if (e instanceof java.net.ConnectException) {
                suggestions.add("检查目标服务器是否运行");
                suggestions.add("验证端口号是否正确");
                suggestions.add("检查防火墙设置");
            } else if (e instanceof java.net.SocketTimeoutException) {
                suggestions.add("增加连接超时时间");
                suggestions.add("检查网络延迟");
                suggestions.add("验证网络路径");
            }

            return new DiagnosticResult(false, e.getMessage(), suggestions, responseTime);
        }
    }

    /** 测试SSH服务响应 */
    public static DiagnosticResult testSSHServiceResponse(String host, int port) {
        long startTime = System.currentTimeMillis();
        List<String> suggestions = new ArrayList<>();

        try (Socket socket = new Socket()) {
            socket.connect(new InetSocketAddress(host, port), 5000);
            socket.setSoTimeout(5000);

            // 读取SSH banner
            byte[] buffer = new byte[256];
            int bytesRead = socket.getInputStream().read(buffer);
            long responseTime = System.currentTimeMillis() - startTime;

            if (bytesRead > 0) {
                String response = new String(buffer, 0, bytesRead).trim();

                if (response.startsWith("SSH-")) {
                    String message = String.format("SSH服务正常响应: %s", response);
                    LOG.debug(message + " (耗时: {}ms)", responseTime);

                    // 分析SSH版本
                    if (response.contains("SSH-1.")) {
                        suggestions.add("服务器使用SSH-1协议，建议升级到SSH-2");
                    }

                    return new DiagnosticResult(true, message, suggestions, responseTime);
                } else {
                    suggestions.add("端口可能不是SSH服务");
                    suggestions.add("检查服务配置");
                    return new DiagnosticResult(
                            false, "非SSH服务响应: " + response, suggestions, responseTime);
                }
            } else {
                suggestions.add("SSH服务无响应，检查服务状态");
                suggestions.add("验证端口配置");
                return new DiagnosticResult(false, "SSH服务无响应", suggestions, responseTime);
            }

        } catch (Exception e) {
            long responseTime = System.currentTimeMillis() - startTime;
            suggestions.add("检查SSH服务是否正常运行");
            suggestions.add("验证服务配置");

            return new DiagnosticResult(false, e.getMessage(), suggestions, responseTime);
        }
    }

    /** 测试连接稳定性 */
    public static DiagnosticResult testConnectionStability(String host, int port) {
        long startTime = System.currentTimeMillis();
        List<String> suggestions = new ArrayList<>();

        int testCount = 5;
        int successCount = 0;
        long totalConnectTime = 0;

        for (int i = 0; i < testCount; i++) {
            try (Socket socket = new Socket()) {
                long connectStart = System.currentTimeMillis();
                socket.connect(new InetSocketAddress(host, port), 5000);
                long connectTime = System.currentTimeMillis() - connectStart;

                totalConnectTime += connectTime;
                successCount++;

                // 短暂等待
                Thread.sleep(100);

            } catch (Exception e) {
                LOG.debug("稳定性测试第{}次失败: {}", i + 1, e.getMessage());
            }
        }

        long responseTime = System.currentTimeMillis() - startTime;
        double successRate = (double) successCount / testCount * 100;
        double avgConnectTime = successCount > 0 ? (double) totalConnectTime / successCount : 0;

        String message =
                String.format(
                        "连接稳定性测试: %d/%d 成功 (%.1f%%), 平均连接时间: %.1fms",
                        successCount, testCount, successRate, avgConnectTime);

        if (successRate < 80) {
            suggestions.add("连接不稳定，检查网络质量");
            suggestions.add("考虑增加重试次数和间隔");
            suggestions.add("检查服务器负载");
        }

        if (avgConnectTime > 2000) {
            suggestions.add("连接时间较长，考虑网络优化");
        }

        boolean isStable = successRate >= 80;
        return new DiagnosticResult(isStable, message, suggestions, responseTime);
    }

    /** 生成问题解决建议 */
    public static List<String> generateTroubleshootingSuggestions(String errorMessage) {
        List<String> suggestions = new ArrayList<>();

        if (errorMessage.contains("connection is closed by foreign host")) {
            suggestions.add("检查SFTP服务器是否正常运行");
            suggestions.add("验证服务器连接数限制配置 (MaxStartups, MaxSessions)");
            suggestions.add("检查防火墙和网络安全策略");
            suggestions.add("确认用户认证信息正确");
            suggestions.add("检查服务器系统负载");
            suggestions.add("考虑使用SSH密钥认证代替密码认证");
            suggestions.add("增加连接重试间隔时间");
            suggestions.add("检查网络中间设备的连接限制");
        } else if (errorMessage.contains("Auth fail") || errorMessage.contains("Authentication")) {
            suggestions.add("验证用户名和密码");
            suggestions.add("检查SSH密钥配置");
            suggestions.add("确认用户有SFTP访问权限");
            suggestions.add("检查用户账户状态");
            suggestions.add("验证SSH服务器认证配置");
        } else if (errorMessage.contains("timeout")) {
            suggestions.add("检查网络连通性和延迟");
            suggestions.add("增加连接超时时间");
            suggestions.add("检查服务器响应速度");
            suggestions.add("优化网络路径");
            suggestions.add("检查服务器负载");
        } else if (errorMessage.contains("Connection refused")) {
            suggestions.add("检查SFTP服务是否启动");
            suggestions.add("验证端口号配置");
            suggestions.add("检查防火墙规则");
            suggestions.add("确认服务监听地址");
        } else if (errorMessage.contains("No route to host")) {
            suggestions.add("检查网络路由配置");
            suggestions.add("验证目标主机可达性");
            suggestions.add("检查网络设备配置");
        }

        // 通用建议
        suggestions.add("查看服务器端SSH/SFTP日志");
        suggestions.add("使用ssh命令行工具测试连接");
        suggestions.add("检查网络质量和稳定性");

        return suggestions;
    }

    /** 快速连接测试 */
    public static boolean quickConnectTest(String host, int port, int timeoutMs) {
        try (Socket socket = new Socket()) {
            socket.connect(new InetSocketAddress(host, port), timeoutMs);
            return true;
        } catch (Exception e) {
            LOG.debug("快速连接测试失败: {}", e.getMessage());
            return false;
        }
    }

    /** 获取连接错误的详细分析 */
    public static String analyzeConnectionError(Exception e) {
        String errorMsg = e.getMessage();
        if (errorMsg == null) {
            errorMsg = e.getClass().getSimpleName();
        }

        StringBuilder analysis = new StringBuilder();
        analysis.append("错误类型: ").append(e.getClass().getSimpleName()).append("\n");
        analysis.append("错误信息: ").append(errorMsg).append("\n");

        if (errorMsg.contains("connection is closed by foreign host")) {
            analysis.append("问题分析: 远程主机主动关闭了连接，通常是服务器端的安全策略或资源限制导致\n");
            analysis.append("可能原因:\n");
            analysis.append("- 服务器连接数达到上限\n");
            analysis.append("- 认证失败次数过多被临时封禁\n");
            analysis.append("- 服务器负载过高\n");
            analysis.append("- 网络安全设备拦截\n");
        } else if (errorMsg.contains("Connection refused")) {
            analysis.append("问题分析: 连接被拒绝，服务器端没有监听指定端口\n");
            analysis.append("可能原因:\n");
            analysis.append("- SFTP服务未启动\n");
            analysis.append("- 端口号错误\n");
            analysis.append("- 防火墙阻止连接\n");
        } else if (errorMsg.contains("timeout")) {
            analysis.append("问题分析: 连接超时，网络或服务器响应问题\n");
            analysis.append("可能原因:\n");
            analysis.append("- 网络延迟过高\n");
            analysis.append("- 服务器响应慢\n");
            analysis.append("- 网络丢包\n");
        }

        return analysis.toString();
    }
}
