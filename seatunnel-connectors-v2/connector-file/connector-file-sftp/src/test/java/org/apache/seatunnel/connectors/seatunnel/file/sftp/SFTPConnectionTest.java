/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.seatunnel.connectors.seatunnel.file.sftp;

import org.apache.seatunnel.connectors.seatunnel.file.sftp.config.SFTPConnectionConfig;
import org.apache.seatunnel.connectors.seatunnel.file.sftp.system.SimpleSFTPConnectionManager;
import org.apache.seatunnel.connectors.seatunnel.file.sftp.utils.SFTPConnectionTroubleshooter;

import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.jcraft.jsch.ChannelSftp;

/**
 * SFTP连接测试工具
 *
 * <p>用于测试和验证SFTP连接的稳定性，特别是针对"connection is closed by foreign host"问题
 */
public class SFTPConnectionTest {

    private static final Logger LOG = LoggerFactory.getLogger(SFTPConnectionTest.class);

    // 测试配置 - 请根据实际环境修改
    private static final String TEST_HOST = "**************";
    private static final int TEST_PORT = 22;
    private static final String TEST_USER = "testuser";
    private static final String TEST_PASSWORD = "testpass";

    @Test
    public void testBasicConnectivity() {
        LOG.info("=== 基础连通性测试 ===");

        SFTPConnectionTroubleshooter.DiagnosticResult result =
                SFTPConnectionTroubleshooter.diagnoseConnection(TEST_HOST, TEST_PORT);

        LOG.info("诊断结果: {}", result);

        if (!result.isSuccess()) {
            LOG.error("连通性测试失败，建议:");
            for (String suggestion : result.getSuggestions()) {
                LOG.error("  - {}", suggestion);
            }
        }
    }

    @Test
    public void testSFTPConnectionWithDefaultConfig() {
        LOG.info("=== 默认配置SFTP连接测试 ===");

        SimpleSFTPConnectionManager manager =
                new SimpleSFTPConnectionManager(
                        TEST_HOST, TEST_PORT, TEST_USER, TEST_PASSWORD, null);

        testConnectionManager(manager, "默认配置");
    }

    @Test
    public void testSFTPConnectionWithStabilityConfig() {
        LOG.info("=== 稳定性优化配置SFTP连接测试 ===");

        SFTPConnectionConfig config = SFTPConnectionConfig.createStabilityOptimized();
        SimpleSFTPConnectionManager manager =
                new SimpleSFTPConnectionManager(
                        TEST_HOST, TEST_PORT, TEST_USER, TEST_PASSWORD, null, config);

        testConnectionManager(manager, "稳定性优化配置");
    }

    @Test
    public void testSFTPConnectionWithHighLatencyConfig() {
        LOG.info("=== 高延迟网络优化配置SFTP连接测试 ===");

        SFTPConnectionConfig config = SFTPConnectionConfig.createHighLatencyOptimized();
        SimpleSFTPConnectionManager manager =
                new SimpleSFTPConnectionManager(
                        TEST_HOST, TEST_PORT, TEST_USER, TEST_PASSWORD, null, config);

        testConnectionManager(manager, "高延迟优化配置");
    }

    @Test
    public void testConnectionStability() {
        LOG.info("=== 连接稳定性压力测试 ===");

        SFTPConnectionConfig config = SFTPConnectionConfig.createStabilityOptimized();
        SimpleSFTPConnectionManager manager =
                new SimpleSFTPConnectionManager(
                        TEST_HOST, TEST_PORT, TEST_USER, TEST_PASSWORD, null, config);

        int testCount = 10;
        int successCount = 0;
        long totalTime = 0;

        for (int i = 1; i <= testCount; i++) {
            try {
                long startTime = System.currentTimeMillis();
                ChannelSftp channel = manager.createConnection();
                long connectTime = System.currentTimeMillis() - startTime;

                // 执行简单操作验证连接
                String pwd = channel.pwd();
                LOG.info("测试 {}/{}: 连接成功 ({}ms), 当前目录: {}", i, testCount, connectTime, pwd);

                manager.closeConnection(channel);
                successCount++;
                totalTime += connectTime;

                // 短暂等待
                Thread.sleep(500);

            } catch (Exception e) {
                LOG.error("测试 {}/{}: 连接失败 - {}", i, testCount, e.getMessage());
            }
        }

        double successRate = (double) successCount / testCount * 100;
        double avgTime = successCount > 0 ? (double) totalTime / successCount : 0;

        LOG.info("=== 稳定性测试结果 ===");
        LOG.info("成功率: {}/{} ({:.1f}%)", successCount, testCount, successRate);
        LOG.info("平均连接时间: {:.1f}ms", avgTime);
        LOG.info("连接统计: {}", manager.getConnectionStats());

        if (successRate < 80) {
            LOG.warn("连接成功率较低，建议检查网络和服务器状态");
        }
    }

    @Test
    public void testErrorHandling() {
        LOG.info("=== 错误处理测试 ===");

        // 测试无效主机
        testInvalidConnection("invalid-host", TEST_PORT, TEST_USER, TEST_PASSWORD, "无效主机");

        // 测试无效端口
        testInvalidConnection(TEST_HOST, 9999, TEST_USER, TEST_PASSWORD, "无效端口");

        // 测试无效用户
        testInvalidConnection(TEST_HOST, TEST_PORT, "invalid-user", TEST_PASSWORD, "无效用户");
    }

    private void testConnectionManager(SimpleSFTPConnectionManager manager, String configName) {
        try {
            LOG.info("开始测试 {} ...", configName);

            long startTime = System.currentTimeMillis();
            ChannelSftp channel = manager.createConnection();
            long connectTime = System.currentTimeMillis() - startTime;

            // 执行基本操作
            String pwd = channel.pwd();
            LOG.info("{} - 连接成功 ({}ms), 当前目录: {}", configName, connectTime, pwd);

            // 测试连接有效性
            boolean isValid = manager.isConnectionValid(channel);
            LOG.info("{} - 连接有效性: {}", configName, isValid);

            manager.closeConnection(channel);
            LOG.info("{} - 连接已关闭", configName);

            LOG.info("{} - 连接统计: {}", configName, manager.getConnectionStats());

        } catch (Exception e) {
            LOG.error("{} - 连接失败: {}", configName, e.getMessage());

            // 分析错误
            String analysis = SFTPConnectionTroubleshooter.analyzeConnectionError(e);
            LOG.error("错误分析:\n{}", analysis);

            // 获取建议
            var suggestions =
                    SFTPConnectionTroubleshooter.generateTroubleshootingSuggestions(e.getMessage());
            if (!suggestions.isEmpty()) {
                LOG.error("解决建议:");
                for (String suggestion : suggestions) {
                    LOG.error("  - {}", suggestion);
                }
            }
        }
    }

    private void testInvalidConnection(
            String host, int port, String user, String password, String testName) {
        LOG.info("测试 {} ...", testName);

        try {
            SimpleSFTPConnectionManager manager =
                    new SimpleSFTPConnectionManager(host, port, user, password, null);

            ChannelSftp channel = manager.createConnection();
            manager.closeConnection(channel);

            LOG.warn("{} - 意外成功，预期应该失败", testName);

        } catch (Exception e) {
            LOG.info("{} - 按预期失败: {}", testName, e.getMessage());

            // 验证错误处理
            String analysis = SFTPConnectionTroubleshooter.analyzeConnectionError(e);
            LOG.debug("错误分析:\n{}", analysis);
        }
    }

    /** 手动测试方法 - 可以通过main方法运行 */
    public static void main(String[] args) {
        SFTPConnectionTest test = new SFTPConnectionTest();

        try {
            test.testBasicConnectivity();
            test.testSFTPConnectionWithStabilityConfig();
            test.testConnectionStability();
        } catch (Exception e) {
            LOG.error("测试执行失败", e);
        }
    }
}
