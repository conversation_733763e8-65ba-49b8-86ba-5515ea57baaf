<?xml version="1.0" encoding="UTF-8"?>
<!--

    Licensed to the Apache Software Foundation (ASF) under one or more
    contributor license agreements.  See the NOTICE file distributed with
    this work for additional information regarding copyright ownership.
    The ASF licenses this file to You under the Apache License, Version 2.0
    (the "License"); you may not use this file except in compliance with
    the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.

-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.apache.seatunnel</groupId>
        <artifactId>seatunnel</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>seatunnel-dist</artifactId>
    <name>SeaTunnel : Dist</name>

    <properties>
        <!-- disable mvn deploy to central maven repo by default -->
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>

    <build>
        <finalName>apache-seatunnel-${project.version}</finalName>
        <plugins>
            <plugin>
                <artifactId>maven-assembly-plugin</artifactId>
                <executions>
                    <execution>
                        <id>bin</id>
                        <goals>
                            <goal>single</goal>
                        </goals>
                        <phase>package</phase>
                        <configuration>
                            <descriptors>
                                <descriptor>src/main/assembly/assembly-bin-ci.xml</descriptor>
                            </descriptors>
                            <appendAssemblyId>true</appendAssemblyId>
                        </configuration>
                    </execution>
                    <execution>
                        <id>src</id>
                        <goals>
                            <goal>single</goal>
                        </goals>
                        <phase>package</phase>
                        <configuration>
                            <descriptors>
                                <descriptor>src/main/assembly/assembly-src.xml</descriptor>
                            </descriptors>
                            <appendAssemblyId>true</appendAssemblyId>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>seatunnel</id>
            <activation>
                <activeByDefault>true</activeByDefault>
                <property>
                    <name>release</name>
                    <value>false</value>
                </property>
            </activation>
            <properties>
                <docker.build.skip>false</docker.build.skip>
                <docker.verify.skip>false</docker.verify.skip>
                <docker.push.skip>false</docker.push.skip>
                <mysql.version>8.0.27</mysql.version>
                <postgresql.version>42.4.3</postgresql.version>
                <postgis.jdbc.version>2.5.1</postgis.jdbc.version>
                <dm-jdbc.version>*********</dm-jdbc.version>
                <sqlserver.version>9.2.1.jre8</sqlserver.version>
                <phoenix.version>5.2.5-HBase-2.x</phoenix.version>
                <oracle.version>********</oracle.version>
                <sqlite.version>********</sqlite.version>
                <db2.version>db2jcc4</db2.version>
                <sqlite.version>********</sqlite.version>
                <tablestore.version>5.13.9</tablestore.version>
                <saphana.version>2.23.10</saphana.version>
                <teradata.version>***********</teradata.version>
                <redshift.version>*******</redshift.version>
                <snowflake.version>3.13.29</snowflake.version>

                <!-- Imap storage dependency package  -->
                <hadoop-aliyun.version>3.1.4</hadoop-aliyun.version>
                <json-smart.version>2.4.7</json-smart.version>
                <aws-java-sdk.version>1.11.271</aws-java-sdk.version>
                <netty-buffer.version>4.1.89.Final</netty-buffer.version>
                <hive.exec.version>3.1.3</hive.exec.version>
                <hive.jdbc.version>3.1.3</hive.jdbc.version>
                <aliyun.sdk.oss.version>3.4.1</aliyun.sdk.oss.version>
                <jdom.version>1.1</jdom.version>
                <tidb.version>3.3.5</tidb.version>
                <presto.version>0.279</presto.version>
                <trino.version>460</trino.version>
            </properties>
            <dependencies>
                <!-- starters -->
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>seatunnel-flink-13-starter</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>seatunnel-flink-15-starter</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>seatunnel-spark-2-starter</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>seatunnel-spark-3-starter</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>seatunnel-starter</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <!-- transforms -->
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>seatunnel-transforms-v2</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <!-- connectors -->
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-sensorsdata</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-fake</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-console</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-assert</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-kafka</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-http-base</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-http-feishu</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-http-wechat</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-prometheus</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-http-myhours</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-http-lemlist</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-http-klaviyo</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-http-onesignal</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-http-notion</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-http-persistiq</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-druid</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-jdbc</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-socket</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-clickhouse</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-databend</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-pulsar</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-hive</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-file-hadoop</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-file-local</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-file-oss</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-file-jindo-oss</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-file-cos</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-file-ftp</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-file-sftp</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-hudi</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-dingtalk</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-web3j</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-kudu</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-email</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-elasticsearch</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-iotdb</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-neo4j</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-redis</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-google-sheets</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-google-firestore</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-datahub</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-sentry</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-mongodb</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-iceberg</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-influxdb</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-cassandra</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-file-s3</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-amazondynamodb</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-starrocks</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-tablestore</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-slack</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-http-gitlab</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-http-github</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-http-jira</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-rabbitmq</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-openmldb</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-doris</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-maxcompute</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-cdc-base</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-cdc-mysql</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-cdc-oracle</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-cdc-mongodb</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-cdc-sqlserver</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-cdc-tidb</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-cdc-postgres</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-cdc-opengauss</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-tdengine</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-selectdb-cloud</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-hbase</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-s3-redshift</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>

                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-rocketmq</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>

                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-file-obs</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>

                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-paimon</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>

                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-amazonsqs</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>

                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-easysearch</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>

                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-milvus</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>

                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-activemq</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>

                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-qdrant</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>

                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-graphql</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>

                <!-- jdbc driver -->
                <dependency>
                    <groupId>com.aliyun.phoenix</groupId>
                    <artifactId>ali-phoenix-shaded-thin-client</artifactId>
                    <version>${phoenix.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>mysql</groupId>
                    <artifactId>mysql-connector-java</artifactId>
                    <version>${mysql.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.postgresql</groupId>
                    <artifactId>postgresql</artifactId>
                    <version>${postgresql.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>net.postgis</groupId>
                    <artifactId>postgis-jdbc</artifactId>
                    <version>${postgis.jdbc.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>com.dameng</groupId>
                    <artifactId>DmJdbcDriver18</artifactId>
                    <version>${dm-jdbc.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>com.sap.cloud.db.jdbc</groupId>
                    <artifactId>ngdbc</artifactId>
                    <version>${saphana.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>com.microsoft.sqlserver</groupId>
                    <artifactId>mssql-jdbc</artifactId>
                    <version>${sqlserver.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>com.oracle.database.jdbc</groupId>
                    <artifactId>ojdbc8</artifactId>
                    <version>${oracle.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>com.oracle.database.xml</groupId>
                    <artifactId>xdb6</artifactId>
                    <version>${oracle.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>com.oracle.database.xml</groupId>
                    <artifactId>xmlparserv2</artifactId>
                    <version>${oracle.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.xerial</groupId>
                    <artifactId>sqlite-jdbc</artifactId>
                    <version>${sqlite.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>com.ibm.db2.jcc</groupId>
                    <artifactId>db2jcc</artifactId>
                    <version>${db2.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>com.aliyun.openservices</groupId>
                    <artifactId>tablestore-jdbc</artifactId>
                    <version>${tablestore.version}</version>
                    <scope>provided</scope>
                </dependency>

                <dependency>
                    <groupId>com.teradata.jdbc</groupId>
                    <artifactId>terajdbc4</artifactId>
                    <version>${teradata.version}</version>
                    <scope>provided</scope>
                </dependency>

                <dependency>
                    <groupId>com.amazon.redshift</groupId>
                    <artifactId>redshift-jdbc42</artifactId>
                    <version>${redshift.version}</version>
                    <scope>provided</scope>
                </dependency>

                <dependency>
                    <groupId>net.snowflake</groupId>
                    <artifactId>snowflake-jdbc</artifactId>
                    <version>${snowflake.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.tikv</groupId>
                    <artifactId>tikv-client-java</artifactId>
                    <version>${tidb.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>com.facebook.presto</groupId>
                    <artifactId>presto-jdbc</artifactId>
                    <version>${presto.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>io.trino</groupId>
                    <artifactId>trino-jdbc</artifactId>
                    <version>${trino.version}</version>
                    <scope>provided</scope>
                </dependency>
                <!-- jdbc driver end -->

                <dependency>
                    <groupId>io.netty</groupId>
                    <artifactId>netty-buffer</artifactId>
                    <version>${netty-buffer.version}</version>
                    <scope>provided</scope>
                </dependency>

                <!-- hadoop jar -->
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>seatunnel-hadoop-aws</artifactId>
                    <version>${project.version}</version>
                    <classifier>optional</classifier>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>com.amazonaws</groupId>
                    <artifactId>aws-java-sdk-bundle</artifactId>
                    <version>${aws-java-sdk.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.hadoop</groupId>
                    <artifactId>hadoop-aliyun</artifactId>
                    <version>${hadoop-aliyun.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>com.aliyun.oss</groupId>
                    <artifactId>aliyun-sdk-oss</artifactId>
                    <version>${aliyun.sdk.oss.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.jdom</groupId>
                    <artifactId>jdom</artifactId>
                    <version>${jdom.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>seatunnel-hadoop3-3.1.4-uber</artifactId>
                    <version>${project.version}</version>
                    <classifier>optional</classifier>
                    <scope>provided</scope>
                </dependency>
                <!-- hadoop jar end -->
                <!-- hive jar start -->
                <dependency>
                    <groupId>org.apache.hive</groupId>
                    <artifactId>hive-exec</artifactId>
                    <version>${hive.exec.version}</version>
                    <scope>provided</scope>
                    <exclusions>
                        <exclusion>
                            <groupId>log4j</groupId>
                            <artifactId>log4j</artifactId>
                        </exclusion>
                        <exclusion>
                            <groupId>org.apache.logging.log4j</groupId>
                            <artifactId>log4j-1.2-api</artifactId>
                        </exclusion>
                        <exclusion>
                            <groupId>org.apache.logging.log4j</groupId>
                            <artifactId>log4j-slf4j-impl</artifactId>
                        </exclusion>
                        <exclusion>
                            <groupId>org.apache.logging.log4j</groupId>
                            <artifactId>log4j-web</artifactId>
                        </exclusion>
                        <exclusion>
                            <groupId>org.slf4j</groupId>
                            <artifactId>slf4j-log4j12</artifactId>
                        </exclusion>
                        <exclusion>
                            <groupId>org.apache.parquet</groupId>
                            <artifactId>parquet-hadoop-bundle</artifactId>
                        </exclusion>
                        <exclusion>
                            <groupId>jdk.tools</groupId>
                            <artifactId>jdk.tools</artifactId>
                        </exclusion>
                        <exclusion>
                            <groupId>org.pentaho</groupId>
                            <artifactId>pentaho-aggdesigner-algorithm</artifactId>
                        </exclusion>
                        <exclusion>
                            <groupId>org.apache.avro</groupId>
                            <artifactId>avro</artifactId>
                        </exclusion>
                    </exclusions>
                </dependency>
                <dependency>
                    <groupId>org.apache.thrift</groupId>
                    <artifactId>libfb303</artifactId>
                    <version>0.9.3</version>
                    <type>pom</type>
                    <scope>provided</scope>
                </dependency>
                <!-- hive jdbc jar -->
                <dependency>
                    <groupId>org.apache.hive</groupId>
                    <artifactId>hive-jdbc</artifactId>
                    <version>${hive.jdbc.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.hive</groupId>
                    <artifactId>hive-service</artifactId>
                    <version>${hive.jdbc.version}</version>
                    <scope>provided</scope>
                    <exclusions>
                        <exclusion>
                            <groupId>log4j</groupId>
                            <artifactId>log4j</artifactId>
                        </exclusion>
                        <exclusion>
                            <groupId>org.apache.logging.log4j</groupId>
                            <artifactId>log4j-1.2-api</artifactId>
                        </exclusion>
                        <exclusion>
                            <groupId>org.apache.logging.log4j</groupId>
                            <artifactId>log4j-slf4j-impl</artifactId>
                        </exclusion>
                        <exclusion>
                            <groupId>org.apache.logging.log4j</groupId>
                            <artifactId>log4j-web</artifactId>
                        </exclusion>
                        <exclusion>
                            <groupId>org.slf4j</groupId>
                            <artifactId>slf4j-log4j12</artifactId>
                        </exclusion>
                        <exclusion>
                            <groupId>org.apache.parquet</groupId>
                            <artifactId>parquet-hadoop-bundle</artifactId>
                        </exclusion>
                        <exclusion>
                            <groupId>jdk.tools</groupId>
                            <artifactId>jdk.tools</artifactId>
                        </exclusion>
                        <exclusion>
                            <groupId>org.pentaho</groupId>
                            <artifactId>pentaho-aggdesigner-algorithm</artifactId>
                        </exclusion>
                        <exclusion>
                            <groupId>org.apache.avro</groupId>
                            <artifactId>avro</artifactId>
                        </exclusion>
                    </exclusions>
                </dependency>
                <!-- hive jar end -->
            </dependencies>
            <repositories>
                <repository>
                    <id>cloudera</id>
                    <url>https://repository.cloudera.com/artifactory/cloudera-repos/</url>
                </repository>
            </repositories>
        </profile>
        <profile>
            <id>release</id>
            <activation>
                <property>
                    <name>release</name>
                    <value>true</value>
                </property>
            </activation>
            <dependencies>
                <!-- starters -->
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>seatunnel-starter</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>seatunnel-flink-13-starter</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>seatunnel-flink-15-starter</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>seatunnel-spark-2-starter</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>seatunnel-spark-3-starter</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <!-- seatunnel connectors for demo -->
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-fake</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-console</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>

                <!-- transforms v2 -->
                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>seatunnel-transforms-v2</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>

                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>seatunnel-hadoop3-3.1.4-uber</artifactId>
                    <version>${project.version}</version>
                    <classifier>optional</classifier>
                    <scope>provided</scope>
                </dependency>

                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-cdc-base</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>

                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-sls</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>

                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>connector-aerospike</artifactId>
                    <version>${project.version}</version>
                    <scope>provided</scope>
                </dependency>

                <dependency>
                    <groupId>org.apache.seatunnel</groupId>
                    <artifactId>seatunnel-hadoop-aws</artifactId>
                    <version>${project.version}</version>
                    <classifier>optional</classifier>
                    <scope>provided</scope>
                </dependency>
            </dependencies>
            <build>
                <finalName>apache-seatunnel-${project.version}</finalName>
                <plugins>
                    <plugin>
                        <artifactId>maven-assembly-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>bin</id>
                                <goals>
                                    <goal>single</goal>
                                </goals>
                                <phase>package</phase>
                                <configuration>
                                    <descriptors>
                                        <descriptor>src/main/assembly/assembly-bin.xml</descriptor>
                                    </descriptors>
                                    <appendAssemblyId>true</appendAssemblyId>
                                </configuration>
                            </execution>

                            <execution>
                                <id>src</id>
                                <goals>
                                    <goal>single</goal>
                                </goals>
                                <phase>package</phase>
                                <configuration>
                                    <descriptors>
                                        <descriptor>src/main/assembly/assembly-src.xml</descriptor>
                                    </descriptors>
                                    <appendAssemblyId>true</appendAssemblyId>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>docker</id>
            <activation>
                <property>
                    <name>release</name>
                    <value>false</value>
                </property>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>exec-maven-plugin</artifactId>
                        <version>${exec-maven-plugin.version}</version>
                        <executions>
                            <execution>
                                <id>docker-build</id>
                                <goals>
                                    <goal>exec</goal>
                                </goals>
                                <phase>package</phase>
                                <configuration>
                                    <skip>${docker.build.skip}</skip>
                                    <environmentVariables>
                                        <DOCKER_BUILDKIT>1</DOCKER_BUILDKIT>
                                    </environmentVariables>
                                    <executable>docker</executable>
                                    <workingDirectory>${project.basedir}</workingDirectory>
                                    <arguments>
                                        <argument>buildx</argument>
                                        <argument>build</argument>
                                        <argument>--load</argument>
                                        <argument>--no-cache</argument>
                                        <argument>-t</argument>
                                        <argument>${docker.hub}/${docker.repo}:${docker.tag}</argument>
                                        <argument>-t</argument>
                                        <argument>${docker.hub}/${docker.repo}:latest</argument>
                                        <argument>${project.basedir}</argument>
                                        <argument>--build-arg</argument>
                                        <argument>VERSION=${project.version}</argument>
                                        <argument>--file=src/main/docker/Dockerfile</argument>
                                    </arguments>
                                </configuration>
                            </execution>
                            <execution>
                                <id>docker-verify</id>
                                <goals>
                                    <goal>exec</goal>
                                </goals>
                                <phase>verify</phase>
                                <configuration>
                                    <skip>${docker.verify.skip}</skip>
                                    <environmentVariables>
                                        <DOCKER_BUILDKIT>1</DOCKER_BUILDKIT>
                                    </environmentVariables>
                                    <executable>docker</executable>
                                    <workingDirectory>${project.basedir}</workingDirectory>
                                    <arguments>
                                        <argument>run</argument>
                                        <argument>--rm</argument>
                                        <argument>${docker.hub}/${docker.repo}:${docker.tag}</argument>
                                        <argument>bash</argument>
                                        <argument>./bin/seatunnel.sh</argument>
                                        <argument>-e</argument>
                                        <argument>local</argument>
                                        <argument>-c</argument>
                                        <argument>config/v2.batch.config.template</argument>
                                    </arguments>
                                </configuration>
                            </execution>
                            <execution>
                                <id>docker-push</id>
                                <goals>
                                    <goal>exec</goal>
                                </goals>
                                <phase>install</phase>
                                <configuration>
                                    <skip>${docker.push.skip}</skip>
                                    <environmentVariables>
                                        <DOCKER_BUILDKIT>1</DOCKER_BUILDKIT>
                                    </environmentVariables>
                                    <executable>docker</executable>
                                    <workingDirectory>${project.basedir}</workingDirectory>
                                    <arguments>
                                        <argument>buildx</argument>
                                        <argument>build</argument>
                                        <argument>--platform</argument>
                                        <argument>linux/amd64,linux/arm64</argument>
                                        <argument>--no-cache</argument>
                                        <argument>--push</argument>
                                        <argument>-t</argument>
                                        <argument>${docker.hub}/${docker.repo}:${docker.tag}</argument>
                                        <argument>-t</argument>
                                        <argument>${docker.hub}/${docker.repo}:latest</argument>
                                        <argument>${project.basedir}</argument>
                                        <argument>--build-arg</argument>
                                        <argument>VERSION=${project.version}</argument>
                                        <argument>--file=src/main/docker/Dockerfile</argument>
                                    </arguments>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
</project>
