/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.seatunnel.transform.nlpmodel.embedding;

import org.apache.seatunnel.shade.com.fasterxml.jackson.core.type.TypeReference;

import org.apache.seatunnel.api.configuration.Option;
import org.apache.seatunnel.api.configuration.Options;
import org.apache.seatunnel.transform.nlpmodel.ModelTransformConfig;

import java.util.Map;

public class EmbeddingTransformConfig extends ModelTransformConfig {

    public static final Option<Integer> SINGLE_VECTORIZED_INPUT_NUMBER =
            Options.key("single_vectorized_input_number")
                    .intType()
                    .defaultValue(1)
                    .withDescription(
                            "The number of single vectorized inputs, default is 1 , which means 1 inputs will be vectorized in one request , eg: qianfan only allows a maximum of 16 simultaneous messages, depending on your own settings, etc");

    public static final Option<Map<String, Object>> VECTORIZATION_FIELDS =
            Options.key("vectorization_fields")
                    .type(new TypeReference<Map<String, Object>>() {})
                    .noDefaultValue()
                    .withDescription(
                            "Specify the field vectorization relationship between input and output. "
                                    + "Supports multiple formats: "
                                    + "1. String format: 'fieldName' (defaults to text modality) "
                                    + "2. Object format with modality and format: {field: 'fieldName', modality: 'modalityType', format: 'formatType'} "
                                    + "where modality can be 'image/jpeg', 'video/mp4' etc. , format can be 'url', 'binary'. ");
}
